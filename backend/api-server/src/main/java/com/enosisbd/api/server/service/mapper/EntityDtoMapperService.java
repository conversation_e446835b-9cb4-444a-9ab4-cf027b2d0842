package com.enosisbd.api.server.service.mapper;

import com.enosisbd.api.server.dto.CrawledPageDto;
import com.enosisbd.api.server.dto.ModuleDto;
import com.enosisbd.api.server.dto.ProjectDto;
import com.enosisbd.api.server.dto.RoleDTO;
import com.enosisbd.api.server.dto.SubModuleDto;
import com.enosisbd.api.server.dto.TestScriptDto;
import com.enosisbd.api.server.dto.UserDTO;
import com.enosisbd.api.server.entity.CrawledPage;
import com.enosisbd.api.server.entity.Project;
import com.enosisbd.api.server.entity.SubModule;
import com.enosisbd.api.server.entity.TestScript;
import com.enosisbd.api.server.entity.User;
import lombok.RequiredArgsConstructor;
import org.modelmapper.ModelMapper;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * Centralized service for Entity-DTO conversions
 * Handles all mapping operations between entities and DTOs using ModelMapper
 * Preserves custom business logic from original conversion methods
 */
@Service
@RequiredArgsConstructor
public class EntityDtoMapperService {

    private final ModelMapper modelMapper;

    // ==================== Generic Conversion Methods ====================

    /**
     * Generic method to convert entity to DTO
     * @param entity Source entity
     * @param dtoClass Target DTO class
     * @param <E> Entity type
     * @param <D> DTO type
     * @return Converted DTO
     */
    public <E, D> D convertToDto(E entity, Class<D> dtoClass) {
        if (entity == null) {
            return null;
        }
        return modelMapper.map(entity, dtoClass);
    }

    /**
     * Generic method to convert DTO to entity
     * @param dto Source DTO
     * @param entityClass Target entity class
     * @param <D> DTO type
     * @param <E> Entity type
     * @return Converted entity
     */
    public <D, E> E convertToEntity(D dto, Class<E> entityClass) {
        if (dto == null) {
            return null;
        }
        return modelMapper.map(dto, entityClass);
    }

    /**
     * Generic method to update existing entity with DTO data
     * @param dto Source DTO
     * @param entity Target entity to update
     * @param <D> DTO type
     * @param <E> Entity type
     * @return Updated entity
     */
    public <D, E> E updateEntityFromDto(D dto, E entity) {
        if (dto == null || entity == null) {
            return entity;
        }
        modelMapper.map(dto, entity);
        return entity;
    }

    /**
     * Convert list of entities to list of DTOs
     * @param entities Source entities
     * @param dtoClass Target DTO class
     * @param <E> Entity type
     * @param <D> DTO type
     * @return List of converted DTOs
     */
    public <E, D> List<D> convertToDtoList(List<E> entities, Class<D> dtoClass) {
        if (entities == null) {
            return null;
        }
        return entities.stream()
                .map(entity -> convertToDto(entity, dtoClass))
                .collect(Collectors.toList());
    }

    // ==================== Specific Conversion Methods with Custom Logic ====================

    /**
     * Convert CrawledPage entity to DTO
     * Preserves original conversion logic
     */
    public CrawledPageDto convertToDto(CrawledPage entity) {
        if (entity == null) {
            return null;
        }
        CrawledPageDto dto = modelMapper.map(entity, CrawledPageDto.class);
        // Set projectId using the custom getter method
        dto.setProjectId(entity.getProjectId());
        return dto;
    }

    /**
     * Convert CrawledPageDto to entity
     * Preserves original conversion logic
     */
    public CrawledPage convertToEntity(CrawledPageDto dto) {
        if (dto == null) {
            return null;
        }
        CrawledPage entity = modelMapper.map(dto, CrawledPage.class);
        // Note: Project relationship should be set by the service layer
        return entity;
    }

    /**
     * Update existing CrawledPage entity with DTO data
     * Preserves original conversion logic
     */
    public CrawledPage convertToEntity(CrawledPage entity, CrawledPageDto dto) {
        if (dto == null || entity == null) {
            return entity;
        }
        // Map basic fields, excluding relationships
        entity.setPageName(dto.getPageName());
        entity.setPageUrl(dto.getPageUrl());
        entity.setCrawlOption(dto.getCrawlOption());
        entity.setDomJson(dto.getDomJson());
        return entity;
    }

    /**
     * Convert Module entity to DTO
     * Preserves original conversion logic
     */
    public ModuleDto convertToDto(com.enosisbd.api.server.entity.Module entity) {
        if (entity == null) {
            return null;
        }
        ModuleDto dto = modelMapper.map(entity, ModuleDto.class);
        // Set projectId using the custom getter method
        dto.setProjectId(entity.getProjectId());
        return dto;
    }

    /**
     * Convert ModuleDto to entity
     * Preserves original conversion logic
     */
    public com.enosisbd.api.server.entity.Module convertToEntity(ModuleDto dto) {
        if (dto == null) {
            return null;
        }
        com.enosisbd.api.server.entity.Module entity = modelMapper.map(dto, com.enosisbd.api.server.entity.Module.class);
        // Note: Project relationship should be set by the service layer
        return entity;
    }

    /**
     * Convert SubModule entity to DTO
     * Preserves original conversion logic
     */
    public SubModuleDto convertToDto(SubModule entity) {
        if (entity == null) {
            return null;
        }
        SubModuleDto dto = modelMapper.map(entity, SubModuleDto.class);
        // Set moduleId using the custom getter method
        dto.setModuleId(entity.getModuleId());
        return dto;
    }

    /**
     * Convert SubModuleDto to entity
     * Preserves original conversion logic
     */
    public SubModule convertToEntity(SubModuleDto dto) {
        if (dto == null) {
            return null;
        }
        SubModule entity = modelMapper.map(dto, SubModule.class);
        // Note: Module relationship should be set by the service layer
        return entity;
    }

    /**
     * Convert TestScript entity to DTO
     * Preserves original conversion logic
     */
    public TestScriptDto convertToDto(TestScript entity) {
        if (entity == null) {
            return null;
        }
        TestScriptDto dto = modelMapper.map(entity, TestScriptDto.class);
        // Set subModuleId using the custom getter method
        dto.setSubModuleId(entity.getSubModuleId());
        return dto;
    }

    /**
     * Convert TestScriptDto to entity
     * Preserves original conversion logic
     */
    public TestScript convertToEntity(TestScriptDto dto) {
        if (dto == null) {
            return null;
        }
        TestScript entity = modelMapper.map(dto, TestScript.class);
        // Note: SubModule relationship should be set by the service layer
        return entity;
    }

    /**
     * Update existing TestScript entity with DTO data
     * Preserves original conversion logic
     */
    public TestScript convertToEntity(TestScript entity, TestScriptDto dto) {
        if (dto == null || entity == null) {
            return entity;
        }
        // Map basic fields, excluding relationships
        entity.setCode(dto.getCode());
        return entity;
    }

    /**
     * Convert Project entity to DTO
     * Preserves original conversion logic
     */
    public ProjectDto convertToDto(Project entity) {
        if (entity == null) {
            return null;
        }
        ProjectDto dto = modelMapper.map(entity, ProjectDto.class);
        return dto;
    }

    /**
     * Convert ProjectDto to entity
     * Preserves original conversion logic
     */
    public Project convertToEntity(ProjectDto dto) {
        if (dto == null) {
            return null;
        }
        Project entity = modelMapper.map(dto, Project.class);
        return entity;
    }

    /**
     * Update existing Project entity with DTO data
     * Preserves original conversion logic
     */
    public Project convertToEntity(Project entity, ProjectDto dto) {
        if (dto == null || entity == null) {
            return entity;
        }
        // Map all fields from DTO to entity
        modelMapper.map(dto, entity);
        return entity;
    }

    /**
     * Convert User entity to UserDTO
     * Preserves original conversion logic from EntitySharingServiceImpl
     */
    public UserDTO convertToDto(User user) {
        if (user == null) {
            return null;
        }
        UserDTO dto = modelMapper.map(user, UserDTO.class);
        return dto;
    }

    /**
     * Convert User entity to UserDTO with roles
     * Preserves original conversion logic from UserServiceImpl
     */
    public UserDTO convertToUserDtoWithRoles(User user) {
        if (user == null) {
            return null;
        }

        Set<RoleDTO> roles = user.getRoles().stream()
                .map(role -> {
                    RoleDTO dto = new RoleDTO();
                    dto.setId(role.getId());
                    dto.setName(role.getName());
                    return dto;
                })
                .collect(Collectors.toSet());

        UserDTO dto = modelMapper.map(user, UserDTO.class);
        dto.setRoles(roles);
        return dto;
    }
}

