package com.enosisbd.api.server.service.module.impl;

import com.enosisbd.api.server.dto.ModuleDto;
import com.enosisbd.api.server.entity.Module;
import com.enosisbd.api.server.exception.BadRequestRestException;
import com.enosisbd.api.server.model.EntityType;
import com.enosisbd.api.server.repository.ModuleRepository;
import com.enosisbd.api.server.repository.ProjectRepository;
import com.enosisbd.api.server.service.authorization.AuthorizationService;
import com.enosisbd.api.server.service.entitySharing.EntitySharingService;
import com.enosisbd.api.server.service.mapper.EntityDtoMapperService;
import com.enosisbd.api.server.service.module.ModuleService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
public class ModuleServiceImpl implements ModuleService {
    private final ModuleRepository repository;
    private final ProjectRepository projectRepository;
    private final EntitySharingService entitySharingService;
    private final AuthorizationService authorizationService;
    private final EntityDtoMapperService entityDtoMapperService;

    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRED, isolation = Isolation.SERIALIZABLE)
    @Override
    public List<ModuleDto> findAll() {
        List<Module> modules = repository.findAll();
        return entitySharingService.getAccessibleEntities(modules, EntityType.MODULE)
                .stream()
                .map(entityDtoMapperService::convertToDto)
                .collect(Collectors.toList());
    }

    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRED, isolation = Isolation.SERIALIZABLE)
    @Override
    public Optional<ModuleDto> getById(Long id) {
        return repository.findByIdJoined(id)
                .map(entityDtoMapperService::convertToDto);
    }

    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRED, isolation = Isolation.SERIALIZABLE)
    @Override
    public boolean existsById(Long id) {
        return repository.existsById(id);
    }

    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRED, isolation = Isolation.SERIALIZABLE)
    @Override
    public ModuleDto add(ModuleDto dto) {
        Module entity = entityDtoMapperService.convertToEntity(dto);

        // Set creator
        entity.setCreatedBy(authorizationService.getCurrentUsername());

        Long projectId = dto.getProjectId();
        if (projectId != null) {
            // Check if user has access to the project
            var project = projectRepository
                    .findById(projectId)
                    .orElseThrow(() -> BadRequestRestException.with("Project not found with ID: " + projectId));

            if (!entitySharingService.hasAccess(project, EntityType.PROJECT)) {
                throw new BadRequestRestException("You don't have access to this project");
            }

            entity.setProject(project);
        }

        repository.save(entity);
        return entityDtoMapperService.convertToDto(entity);
    }

    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRED, isolation = Isolation.SERIALIZABLE)
    @Override
    public Optional<ModuleDto> update(ModuleDto dto) {
        var maybe = repository.findById(dto.getId());
        if (maybe.isEmpty()) return Optional.empty();

        Module existingEntity = maybe.get();
        Module entity = entityDtoMapperService.convertToEntity(dto);

        // Check if project ID is valid and matches the existing entity
        Long projectId = dto.getProjectId();
        if (projectId != null && !projectId.equals(existingEntity.getProjectId())) {
            throw new BadRequestRestException("Cannot change the project of a module");
        }

        // Set the project from the existing entity
        entity.setProject(existingEntity.getProject());

        repository.save(entity);
        return Optional.of(entityDtoMapperService.convertToDto(entity));
    }

    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRED, isolation = Isolation.SERIALIZABLE)
    @Override
    public Optional<Boolean> delete(Long id) {
        if (!repository.existsById(id)) return Optional.empty();
        repository.deleteById(id);
        return Optional.of(true);
    }

    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRED, isolation = Isolation.SERIALIZABLE)
    @Override
    public List<ModuleDto> findByProjectId(Long projectId) {
        return repository.findByProjectIdJoined(projectId)
                .stream()
                .map(entityDtoMapperService::convertToDto)
                .collect(Collectors.toList());
    }

    /**
     * Delete all modules for a project
     *
     * @param projectId the project id
     * @return true if modules were deleted, false otherwise
     */
    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRED, isolation = Isolation.SERIALIZABLE)
    @Override
    public boolean deleteByProjectId(Long projectId) {
        List<Module> modules = repository.findByProjectId(projectId);
        if (modules.isEmpty()) {
            return false;
        }

        // Delete all modules
        repository.deleteAll(modules);
        return true;
    }


}
